import { fileURLToPath, URL } from "node:url";
import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";
import vueDevTools from "vite-plugin-vue-devtools";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import ElementPlus from "unplugin-element-plus/vite";
import { createSvgIconsPlugin } from "vite-plugin-svg-icons";
import postcsspxtoviewport8plugin from "postcss-px-to-viewport-8-plugin";
import USERS from "./user.config";
import { WINDOW_WIDTH } from "./src/constants";
import { createDevTools } from "@vtj/pro/vite";

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // 加载环境变量，空字符串表示加载所有环境变量
  // eslint-disable-next-line no-undef
  const env = loadEnv(mode, process.cwd(), "");
  const headers = USERS[env?.USER] ?? USERS.delong;

  return {
    plugins: [
      vue(),
      vueJsx(),
      vueDevTools(),
      AutoImport({
        imports: ["vue", "vue-router"],
        resolvers: [ElementPlusResolver()],
        dts: "./auto-imports.d.ts",
      }),
      Components({
        resolvers: [ElementPlusResolver()],
        dts: "./components.d.ts",
      }),
      ElementPlus({
        useSource: true,
        importStyle: "sass",
      }),
      createSvgIconsPlugin({
        // 指定需要缓存的图标文件夹
        iconDirs: [
          fileURLToPath(new URL("./src/assets/icons", import.meta.url)),
        ],
        // 指定symbolId格式
        symbolId: "icon-[dir]-[name]",
      }),
      createDevTools(),
    ],
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", import.meta.url)),
      },
      extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"],
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/assets/styles/index.scss" as *;`,
        },
      },
      postcss: {
        plugins: [
          postcsspxtoviewport8plugin({
            unitToConvert: "px",
            viewportWidth: WINDOW_WIDTH,
            unitPrecision: 5, // 单位转换后保留的精度
            propList: ["*"], // 能转化为vw的属性列表
            viewportUnit: "vw", // 希望使用的视口单位
            fontViewportUnit: "vw", // 字体使用的视口单位
            selectorBlackList: ["no-rem"], // 需要忽略的CSS选择器，不会转为视口单位，使用原有的px等单位。
            minPixelValue: 1, // 设置最小的转换数值，如果为1的话，只有大于1的值会被转换
            mediaQuery: true, // 媒体查询里的单位是否需要转换单位
            replace: true, //  是否直接更换属性值，而不添加备用属性
            exclude: [], // 忽略某些文件夹下的文件或特定文件，例如 'node_modules' 下的文件
            include: [], // 如果设置了include，那将只有匹配到的文件才会被转换
            landscape: false, // 是否添加根据 landscapeWidth 生成的媒体查询条件 @media (orientation: landscape)
            landscapeUnit: "vw", // 横屏时使用的单位
            landscapeWidth: 1024, // 横屏时使用的视口宽度
          }),
        ],
      },
    },
    server: {
      port: parseInt(env.VITE_PORT || "3000"),
      open: env.VITE_OPEN === "true",
      cors: true,
      proxy: {
        "^/datax": {
          target: env.VITE_API_BASE_URL,
          changeOrigin: true,
          secure: false,
          pathRewrite: {
            "/": "",
          },
          headers: {
            ...headers,
            "shield-ds-prstypecode": 3,
          },
        },
      },
    },
    build: {
      target: "es2015",
      minify: "terser",
      cssCodeSplit: true,
      outDir: env.VITE_OUTPUT_DIR || "dist",
      assetsDir: "static",
      terserOptions: {
        compress: {
          drop_console: env.VITE_DROP_CONSOLE === "true",
          drop_debugger: env.VITE_DROP_CONSOLE === "true",
        },
      },
      rollupOptions: {
        output: {
          manualChunks: {
            vue: ["vue", "vue-router", "pinia"],
            elementPlus: ["element-plus"],
          },
          // 自定义资源文件名和目录结构
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name.split('.');
            const ext = info[info.length - 1];
            if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)) {
              return `static/media/[name]-[hash][extname]`;
            }
            if (/\.(png|jpe?g|gif|svg|ico|webp)(\?.*)?$/i.test(assetInfo.name)) {
              return `static/images/[name]-[hash][extname]`;
            }
            if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name)) {
              return `static/fonts/[name]-[hash][extname]`;
            }
            if (ext === 'css') {
              return `static/css/[name]-[hash][extname]`;
            }
            return `static/assets/[name]-[hash][extname]`;
          },
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
        },
      },
    },
    // 定义全局常量替换
    define: {
      __APP_VERSION__: JSON.stringify(env.VITE_APP_VERSION),
      __APP_ENV__: JSON.stringify(env.VITE_APP_ENV),
    },
  };
});
