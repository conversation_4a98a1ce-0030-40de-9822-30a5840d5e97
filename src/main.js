/*
 * @Date: 2025-06-12 14:12:45
 * @LastEditors: 谢晓静
 * @LastEditTime: 2025-06-23 17:20:45
 * @FilePath: /Apple/datax-fe/src/main.js
 */
import { createApp } from "vue";
import { createProvider, LocalService, createModules } from "@vtj/web";
import "@vtj/web/src/index.scss";
import "element-plus/dist/index.css";
import ElementPlus from "element-plus";
import { createPinia } from "pinia";
import App from "./App.vue";
import router from "./router";

const app = createApp(App);
const service = new LocalService();
const { provider, onReady } = createProvider({
  nodeEnv: import.meta.env.MODE,
  modules: createModules(),
  service,
  router,
});

onReady(async () => {
  app.use(createPinia());
  app.use(router);
  app.use(ElementPlus);
  app.use(provider);
  app.mount("#app");
});
