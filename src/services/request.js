import axios from "axios";
// import SvgIcon from "@/components/SvgIcon.vue";
// import router from "@/router";
// import { useAddFilter } from "@/use/useAddFilter";
// import { h } from "vue";
import { ElMessage } from "element-plus";
// import * as Sentry from "@sentry/vue";

// export const reportLog = (res) => {
//   if (res.status !== 200) return;
//   const { config, data } = res;
//   const { url, params } = config;
//   const { code = 0 } = data;
//   if (code !== 0) {
//     Sentry.captureMessage("Code Error", {
//       contexts: {
//         res: {
//           code,
//           url,
//           params: JSON.stringify(params),
//           message: res?.data?.msg,
//         },
//       },
//       level: "info",
//     });
//   }
// };

export const CODE = {
  Success: 0,
  // 无权限
  NoPermission: 401,
  // 系统失败
  SystemFail: 2002,
  // 业务失败
  BusinessFail: 2006,
  // 非空校验不通过
  NullCheck: 3001,
  // 倍数校验不通过
  TimesCheck: 3002,
  // 数据类型不正确
  DataFormatCheck: 3004,
  // 加锁失败
  LockFailure: 4004,
  // 超时
  TimeOut: 5001,
  UnknownError: 20000,
};

// 判断是否生产环境
//const isProd = process.env.NODE_ENV === "production";

// let loadingInstance = null;
// let requestNum = 0;

// 自定义错误提示
// const errMessageConfig = {
//   icon: h(SvgIcon, { name: "alert" }),
//   grouping: true,
//   duration: 5000,
// };

// loading组件增加高斯模糊
// const domContent = document.querySelector("#app") as HTMLElement;
// const { addBlur, removeBlur } = useAddFilter();

// 增加loading 如果pending请求数量等于1，弹出loading
// const addLoading = (text = "页面加载中...") => {
//   requestNum++;
//   if (requestNum === 1) {
//     // const dialogDom = document.querySelector(".el-dialog") as HTMLElement;
//     // addBlur([domContent, dialogDom], "filter-blur-loading");
//     loadingInstance = ElLoading.service({
//       text,
//       body: true,
//       spinner: `<path transform="translate(0.751953, 0.843750)" stroke="none" stroke-width="1" fill="#0071E3" fill-rule="nonzero" d="M13.2070312,26.4257812 C13.4492188,26.4257812 13.6503906,26.34375 13.8105469,26.1796875 C13.9707031,26.015625 14.0507812,25.8164062 14.0507812,25.5820312 L14.0507812,21.421875 C14.0507812,21.1875 13.9707031,20.9882812 13.8105469,20.8242188 C13.6503906,20.6601562 13.4492188,20.578125 13.2070312,20.578125 C12.9726562,20.578125 12.7753906,20.6601562 12.6152344,20.8242188 C12.4550781,20.9882812 12.375,21.1875 12.375,21.421875 L12.375,25.5820312 C12.375,25.8164062 12.4550781,26.015625 12.6152344,26.1796875 C12.7753906,26.34375 12.9726562,26.4257812 13.2070312,26.4257812 Z M19.8164062,24.65625 C20.0273438,24.5390625 20.1601562,24.3691406 20.2148438,24.1464844 C20.2695312,23.9238281 20.2421875,23.7109375 20.1328125,23.5078125 L18.046875,19.8984375 C17.9296875,19.6953125 17.7617188,19.5625 17.5429688,19.5 C17.3242188,19.4375 17.109375,19.4648438 16.8984375,19.5820312 C16.6875,19.7070312 16.5546875,19.8789062 16.5,20.0976562 C16.4453125,20.3164062 16.4765625,20.5273438 16.59375,20.7304688 L18.6679688,24.3398438 C18.7929688,24.5429688 18.9648438,24.6757812 19.1835938,24.7382812 C19.4023438,24.8007812 19.6132812,24.7734375 19.8164062,24.65625 Z M24.65625,19.8164062 C24.78125,19.6132812 24.8125,19.4023438 24.75,19.1835938 C24.6875,18.9648438 24.5546875,18.7929688 24.3515625,18.6679688 L20.7304688,16.5820312 C20.5273438,16.4726562 20.3164062,16.4453125 20.0976562,16.5 C19.8789062,16.5546875 19.7109375,16.6835938 19.59375,16.8867188 C19.4765625,17.0976562 19.4472656,17.3125 19.5058594,17.53125 C19.5644531,17.75 19.6953125,17.921875 19.8984375,18.046875 L23.5078125,20.1328125 C23.7109375,20.2421875 23.9238281,20.2695312 24.1464844,20.2148438 C24.3691406,20.1601562 24.5390625,20.0273438 24.65625,19.8164062 Z M26.4257812,13.2070312 C26.4257812,12.9726562 26.3457031,12.7753906 26.1855469,12.6152344 C26.0253906,12.4550781 25.8242188,12.375 25.5820312,12.375 L21.421875,12.375 C21.1875,12.375 20.9882812,12.4550781 20.8242188,12.6152344 C20.6601562,12.7753906 20.578125,12.9726562 20.578125,13.2070312 C20.578125,13.4492188 20.6601562,13.6503906 20.8242188,13.8105469 C20.9882812,13.9707031 21.1875,14.0507812 21.421875,14.0507812 L25.5820312,14.0507812 C25.8242188,14.0507812 26.0253906,13.9707031 26.1855469,13.8105469 C26.3457031,13.6503906 26.4257812,13.4492188 26.4257812,13.2070312 Z M24.6679688,6.609375 C24.5507812,6.3984375 24.3789062,6.265625 24.1523438,6.2109375 C23.9257812,6.15625 23.7109375,6.18359375 23.5078125,6.29296875 L19.8984375,8.37890625 C19.6953125,8.49609375 19.5625,8.66601562 19.5,8.88867188 C19.4375,9.11132812 19.4726562,9.328125 19.6054688,9.5390625 C19.7226562,9.7421875 19.8886719,9.87109375 20.1035156,9.92578125 C20.3183594,9.98046875 20.5273438,9.953125 20.7304688,9.84375 L24.3515625,7.7578125 C24.5546875,7.6328125 24.6875,7.4609375 24.75,7.2421875 C24.8125,7.0234375 24.7851562,6.8125 24.6679688,6.609375 Z M19.828125,1.76953125 C19.6171875,1.65234375 19.4023438,1.62304688 19.1835938,1.68164063 C18.9648438,1.74023438 18.7929688,1.87109375 18.6679688,2.07421875 L16.59375,5.6953125 C16.4765625,5.8984375 16.4453125,6.109375 16.5,6.328125 C16.5546875,6.546875 16.6875,6.71875 16.8984375,6.84375 C17.109375,6.953125 17.3222656,6.97851562 17.5371094,6.91992188 C17.7519531,6.86132812 17.921875,6.73046875 18.046875,6.52734375 L20.1328125,2.91796875 C20.2421875,2.71484375 20.2695312,2.50195313 20.2148438,2.27929688 C20.1601562,2.05664063 20.03125,1.88671875 19.828125,1.76953125 Z M13.2070312,0 C12.9726562,0 12.7753906,0.08203125 12.6152344,0.24609375 C12.4550781,0.41015625 12.375,0.609375 12.375,0.84375 L12.375,5.00390625 C12.375,5.23828125 12.4550781,5.4375 12.6152344,5.6015625 C12.7753906,5.765625 12.9726562,5.84765625 13.2070312,5.84765625 C13.4492188,5.84765625 13.6503906,5.765625 13.8105469,5.6015625 C13.9707031,5.4375 14.0507812,5.23828125 14.0507812,5.00390625 L14.0507812,0.84375 C14.0507812,0.609375 13.9707031,0.41015625 13.8105469,0.24609375 C13.6503906,0.08203125 13.4492188,0 13.2070312,0 Z M6.609375,1.7578125 C6.3984375,1.875 6.265625,2.046875 6.2109375,2.2734375 C6.15625,2.5 6.1875,2.71484375 6.3046875,2.91796875 L8.37890625,6.52734375 C8.50390625,6.73046875 8.67578125,6.86328125 8.89453125,6.92578125 C9.11328125,6.98828125 9.32421875,6.95703125 9.52734375,6.83203125 C9.73828125,6.71484375 9.87109375,6.546875 9.92578125,6.328125 C9.98046875,6.109375 9.953125,5.8984375 9.84375,5.6953125 L7.7578125,2.07421875 C7.6328125,1.87109375 7.46289063,1.73828125 7.24804688,1.67578125 C7.03320313,1.61328125 6.8203125,1.640625 6.609375,1.7578125 Z M1.78125,6.59765625 C1.65625,6.80859375 1.625,7.0234375 1.6875,7.2421875 C1.75,7.4609375 1.8828125,7.6328125 2.0859375,7.7578125 L5.6953125,9.84375 C5.8984375,9.953125 6.109375,9.98046875 6.328125,9.92578125 C6.546875,9.87109375 6.71875,9.7421875 6.84375,9.5390625 C6.9609375,9.328125 6.98828125,9.11328125 6.92578125,8.89453125 C6.86328125,8.67578125 6.73046875,8.50390625 6.52734375,8.37890625 L2.91796875,6.29296875 C2.71484375,6.18359375 2.50195312,6.15625 2.27929688,6.2109375 C2.05664062,6.265625 1.890625,6.39453125 1.78125,6.59765625 Z M0,13.2070312 C0,13.4492188 0.08203125,13.6503906 0.24609375,13.8105469 C0.41015625,13.9707031 0.609375,14.0507812 0.84375,14.0507812 L5.00390625,14.0507812 C5.24609375,14.0507812 5.44726562,13.9707031 5.60742188,13.8105469 C5.76757812,13.6503906 5.84765625,13.4492188 5.84765625,13.2070312 C5.84765625,12.9726562 5.76757812,12.7753906 5.60742188,12.6152344 C5.44726562,12.4550781 5.24609375,12.375 5.00390625,12.375 L0.84375,12.375 C0.609375,12.375 0.41015625,12.4550781 0.24609375,12.6152344 C0.08203125,12.7753906 0,12.9726562 0,13.2070312 Z M1.78125,19.8164062 C1.890625,20.0273438 2.05664062,20.1601562 2.27929688,20.2148438 C2.50195312,20.2695312 2.71484375,20.2421875 2.91796875,20.1328125 L6.52734375,18.046875 C6.73046875,17.921875 6.86328125,17.75 6.92578125,17.53125 C6.98828125,17.3125 6.9609375,17.1015625 6.84375,16.8984375 C6.71875,16.6875 6.546875,16.5546875 6.328125,16.5 C6.109375,16.4453125 5.8984375,16.4726562 5.6953125,16.5820312 L2.0859375,18.6679688 C1.8828125,18.7929688 1.75,18.9648438 1.6875,19.1835938 C1.625,19.4023438 1.65625,19.6132812 1.78125,19.8164062 Z M6.609375,24.65625 C6.8203125,24.7734375 7.03320313,24.8007812 7.24804688,24.7382812 C7.46289063,24.6757812 7.6328125,24.5429688 7.7578125,24.3398438 L9.84375,20.7304688 C9.953125,20.5273438 9.98046875,20.3164062 9.92578125,20.0976562 C9.87109375,19.8789062 9.7421875,19.7109375 9.5390625,19.59375 C9.328125,19.4765625 9.11328125,19.4472656 8.89453125,19.5058594 C8.67578125,19.5644531 8.50390625,19.6953125 8.37890625,19.8984375 L6.3046875,23.5078125 C6.1875,23.7109375 6.15625,23.9238281 6.2109375,24.1464844 C6.265625,24.3691406 6.3984375,24.5390625 6.609375,24.65625 Z" id="形状"></path>`,
//       svgViewBox: "0 0 28 28",
//       background: "rgba(21, 21, 22, .8)",
//       // background: "rgba(0, 0, 0, 0)",
//     });
//   }
// };

// 取消loading 如果pending请求数量等于0，关闭loading
// const cancelLoading = () => {
//   Promise.resolve().then(() => {
//     requestNum--;
//     if (requestNum === 0) {
//       // const dialogDom = document.querySelector(".el-dialog") as HTMLElement;
//       // removeBlur([domContent, dialogDom], "filter-blur-loading");
//       loadingInstance?.close();
//     }
//   });
// };

const request = axios.create({
  timeout: 30 * 1000,
  headers: {
    "Content-Type": "application/json; charset=utf-8;",
  },
});

// 添加请求拦截器
request.interceptors.request.use(
  (config) => {
    // const { loading = true, loadingText } = config;
    // if (loading) addLoading(loadingText);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 添加响应拦截器
request.interceptors.response.use(
  async (response) => {
    // const { loading = true } = response.config;
    // if (loading) cancelLoading();
    // reportLog(response);

    const { code, data } = response.data;
    switch (code) {
      case CODE.Success:
        return response?.request?.responseType === "blob"
          ? response
          : response.data;

      case CODE.NoPermission:
        // router.replace({ name: "NoPermission" });
        return;

      case CODE.LockFailure:
        return Promise.reject("加锁失败");

      case CODE.TimeOut:
        ElMessage({
          message: "提交失败，已超过提交时间",
          //   onClose() {
          //     router.customReplace();
          //   },
          //   ...errMessageConfig,
        });
        return Promise.reject(response.data);

      case CODE.SystemFail:
      case CODE.BusinessFail:
      case CODE.NullCheck:
      case CODE.TimesCheck:
      case CODE.DataFormatCheck:
        ElMessage({
          // message: msg || "服务端异常",
          message: "网络异常，请重试",
          //   ...errMessageConfig,
        });
        return Promise.reject(response.data);

      case CODE.UnknownError:
        ElMessage({
          /* message: msg || "服务端异常", */
          message: response.data.msg,
          //   ...errMessageConfig,
        });
        return data;
      default:
        break;
    }
  },
  (error) => {
    const msg = error?.response?.data?.msg;
    if (msg) {
      ElMessage.error(msg);
    } else {
      ElMessage.error("网络异常，请重试");
    }
    return Promise.reject(error?.response?.data || error);
  }
);

export default request;
