<template>
  <div class="create-table div_1z67tsqwi">
    <div class="card div_6066jo571">
      <h1>Create a New Table</h1>
      <p>Table basic info:</p>
      <ElForm
        :model="state.ruleForm"
        labelWidth="80px"
        :rules="{
          table_name: [
            {
              required: true,
              message: 'Table Name不能为空',
              trigger: ['blur', 'change'],
            },
            {
              pattern: /^[a-z0-9_]+$/,
              message:
                'Invalid character! Only lowercase letters and underscores are allowed',
              trigger: ['blur', 'change'],
            },
          ],
          remark: [
            {
              required: true,
              message: 'Description不能为空',
              trigger: ['blur', 'change'],
            },
          ],
        }"
        ref="formRef"
      >
        <ElRow :gutter="10">
          <ElCol :span="12">
            <ElFormItem
              prop="table_name"
              label="Table Name"
              :required="true"
              :inlineMessage="false"
              labelPosition="top"
            >
              <ElInput
                v-model:modelValue="state.ruleForm.table_name"
                :rows="1"
                size="default"
                type="textarea"
                maxlength="50"
                placeholder="Please input: aaaa bbbb_ccc ddddd"
                :showWordLimit="true"
              ></ElInput>
            </ElFormItem>
            <p class="note-info">
              Note: The name used in a database to identify and reference a
              specific table
            </p>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem
              label="Business Name"
              :required="false"
              :inlineMessage="false"
              labelPosition="top"
            >
              <ElInput
                v-model:modelValue="state.business_line_title"
                :rows="1"
                type="textarea"
                maxlength="50"
                placeholder="Please input:"
                :showWordLimit="true"
                disabled
              >
                Business Name</ElInput
              >
            </ElFormItem>
          </ElCol>
        </ElRow>
        <ElRow :gutter="10">
          <ElCol :span="12">
            <ElFormItem
              label="Description"
              prop="remark"
              :required="true"
              :inlineMessage="false"
              labelPosition="top"
            >
              <ElInput
                v-model:modelValue="state.ruleForm.remark"
                :rows="1"
                type="textarea"
                maxlength="200"
                placeholder="Please summarize the main content of this data"
                :showWordLimit="true"
              >
                Table Name</ElInput
              >
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem
              label="Upload Frequency"
              prop="upload_frequency"
              :inlineMessage="false"
              labelPosition="top"
              :required="true"
            >
              <ElSelect
                v-model:modelValue="state.ruleForm.upload_frequency"
                :multiple="false"
                :showArrow="true"
                :modelValue="state.upload_frequency"
                :persistent="true"
                :teleported="true"
                placeholder="Please select"
                :reserveKeyword="true"
                :maxCollapseTags="1"
                :remoteShowSuffix="false"
                :automaticDropdown="false"
                :defaultFirstOption="false"
              >
                <ElOption
                  v-for="item in [
                    { label: 'By Week', value: 7 },
                    { label: 'By Quarterly', value: 90 },
                  ]"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></ElOption>
              </ElSelect>
            </ElFormItem>
          </ElCol>
        </ElRow>
        <ElRow :gutter="10">
          <ElCol :span="12">
            <ElFormItem
              label="Reminder Subscription"
              :required="true"
              labelWidth="200"
              :inlineMessage="false"
              labelPosition="left"
            >
              <ElSwitch
                v-model:modelValue="state.ruleForm.need_remind"
              ></ElSwitch>
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>
      <p>Please fill in the table content:</p>
      <div class="card">
        <ElRow :gutter="10">
          <ElCol :span="6">
            <p>Set Column</p>
          </ElCol>
          <ElCol :span="6">
            <p>Set Column Name</p>
          </ElCol>
          <ElCol :span="6">
            <p>Set Column Format</p>
          </ElCol>
        </ElRow>
        <ElRow
          v-for="(item, index) in state.tableData"
          :key="item.column"
          :gutter="10"
          class="ElRow_5k66jlkl3"
        >
          <ElCol :span="6">
            <ElSelect v-model:modelValue="item.column" :disabled="true">
              <ElOption
                v-for="(item, index) in availableLetters"
                :key="item"
                :label="item"
                :value="item"
              ></ElOption>
            </ElSelect>
          </ElCol>
          <ElCol :span="6">
            <ElInput v-model:modelValue="item.name"></ElInput>
          </ElCol>
          <ElCol :span="6">
            <ElSelect v-model:modelValue="item.format">
              <ElOption
                v-for="(item, index) in [
                  { label: 'String', value: 'varchar' },
                  { label: 'Text', value: 'text' },
                  { label: 'Date', value: 'date' },
                  { label: 'Time', value: 'datetime' },
                  { label: 'Int', value: 'int' },
                  { label: 'Float', value: 'float' },
                ]"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></ElOption>
            </ElSelect>
          </ElCol>
        </ElRow>
      </div>
      <div class="div_567lo4km">
        <VtjIconNpAddRow
          class="add-icon"
          @click="addTableData"
        ></VtjIconNpAddRow>
        <p class="add-icon" @click="addTableData">Add</p>
      </div>
      <ElButton type="primary" class="download-btn" @click="click_506qimwjh">
        Create</ElButton
      >
    </div>
    <div class="card div_2s67u3ns4">
      <h1>Preview Table</h1>
      <ElTable :data="rightTableData">
        <ElTableColumn
          v-for="(item, index) in columns"
          :prop="item.field"
          type="default"
          :label="item.title"
        >
        </ElTableColumn>
      </ElTable>
    </div>
  </div>
</template>
<script lang="ts">
// @ts-nocheck
import { defineComponent, reactive } from "vue";
import {
  ElForm,
  ElRow,
  ElCol,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElSwitch,
  ElButton,
  ElTable,
  ElTableColumn,
  ElMessage,
} from "element-plus";
import { VtjIconNpAddRow } from "@vtj/icons";
import { useProvider } from "@vtj/renderer";
import { downLoadTable } from "@/services/CreateTableService";
import { useRoute } from "vue-router";

export default defineComponent({
  name: "CreateTable",
  components: {
    ElForm,
    ElRow,
    ElCol,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElSwitch,
    VtjIconNpAddRow,
    ElButton,
    ElTable,
    ElTableColumn,
  },
  setup(props) {
    const provider = useProvider({ id: "164nxtxf", version: "1750405515028" });
    const route = useRoute();
    const state = reactive({
      ruleForm: {
        table_name: "",
        business_name: route.query.business_line,
        remark: "",
        upload_frequency: 7,
        need_remind: true,
      },
      business_line_title: route.query.business_line_title,
      tableData: [],
      allLetters: [],
      usedLetters: [],
      frequencyOptions: [
        { label: "By Week", value: "1" },
        { label: "By Quarterly", value: "2" },
      ],
    });
    return { state, props, provider, route };
  },
  computed: {
    columns() {
      return this.state.tableData.map((it) => ({
        field: it.column,
        title: `Column ${it.column}`,
        minWidth: 100,
      }));
    },
    rightTableData() {
      const rowData = {};
      this.state.tableData.map((item) => {
        rowData[item.column] = item.name;
      });
      return [rowData];
    },
    availableLetters() {
      return this.state.allLetters.filter(
        (letter) => !this.state.usedLetters.includes(letter)
      );
    },
  },
  methods: {
    click_506qimwjh() {
      this.$refs.formRef.validate((valid) => {
        if (!valid) {
          return;
        }
        // 手动校验
        for (let i = 0; i < this.state.tableData.length; i++) {
          const name = this.state.tableData[i].name;
          if (!name) {
            ElMessage.error(`第${i + 1}列名不能为空`);
            return;
          }
          if (!/^[a-z0-9_]+$/.test(name)) {
            ElMessage.error(
              `第${i + 1}列名格式不正确，只能包含小写字母、数字和下划线`
            );
            return;
          }
        }
        // 通过后再提交
        this.fullLoading = true;
        const columns = this.state.tableData.map((item) => ({
          excel_column_name: item.column,
          db_column_name: item.name,
          db_column_type: item.format,
        }));
        const params = {
          ...this.state.ruleForm,
          columns,
        };
        console.log(params, "params");
        downLoadTable(params)
          .then(({ code }) => {
            if (code !== 0) {
              // TODO: error handle
              return;
            }
            ElMessage.success("Create table success");
          })
          .finally(() => {
            this.fullLoading = false;
          });
      });
    },
    addTableData() {
      const nextLetter = this.availableLetters[0];
      if (nextLetter) {
        this.state.tableData.push({
          column: nextLetter,
          name: "",
          format: "varchar",
        });
        this.state.usedLetters.push(nextLetter);
      } else {
        console.warn("No more letters available");
      }
    },
    generateLetters() {
      const letters = [];
      // A-Z
      for (let i = 65; i <= 90; i++) {
        letters.push(String.fromCharCode(i));
      }
      // AA-AZ, BA-BZ, ..., ZA-ZZ
      for (let i = 65; i <= 90; i++) {
        for (let j = 65; j <= 90; j++) {
          letters.push(String.fromCharCode(i) + String.fromCharCode(j));
        }
      }
      return letters;
    },
  },
  mounted() {
    this.state.allLetters = this.generateLetters();
  },
});
</script>
<style lang="scss" scoped>
.create-table {
  padding: 10px;
  background-color: #f5f5f5;
  height: 100%;
}

.card {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  position: relative;
}

.download-btn {
  position: absolute;
  bottom: 10px;
  left: 47%;
}

.add-icon {
  color: #409eff;
  padding-right: 4px;
  cursor: pointer;
}

.note-info {
  color: red;
  font-size: 10px;
}

.div_1z67tsqwi {
  display: flex;
}

.div_6066jo571 {
  width: 60%;
  padding-top: 10px;
  border-style: none;
  margin-right: 10px;
  padding-left: 10px;
  border-radius: 4px;
  padding-right: 10px;
  padding-bottom: 10px;
}

.ElRow_5k66jlkl3 {
  padding-bottom: 8px;
}

.div_567lo4km {
  display: flex;
  margin-top: 0px;
  align-items: center;
  padding-left: 20px;
  justify-content: flex-start;
}

.div_2s67u3ns4 {
  width: 40%;
  padding-top: 10px;
  border-style: none;
  padding-left: 10px;
  border-radius: 4px;
  padding-right: 10px;
  padding-bottom: 10px;
}
</style>
