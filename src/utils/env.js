/**
 * 获取环境变量
 * @returns {Object} 环境变量对象
 */
export function getEnv() {
  return {
    // 应用信息
    appName: import.meta.env.VITE_APP_NAME,
    appVersion: import.meta.env.VITE_APP_VERSION,
    appDescription: import.meta.env.VITE_APP_DESCRIPTION,
    
    // 环境标识
    mode: import.meta.env.MODE,
    isDev: import.meta.env.DEV,
    isProd: import.meta.env.PROD,
    env: import.meta.env.VITE_APP_ENV,
    
    // API配置
    apiBaseUrl: import.meta.env.VITE_API_BASE_URL,
    apiTimeout: Number(import.meta.env.VITE_API_TIMEOUT || 10000),
    apiMock: import.meta.env.VITE_API_MOCK === 'true',
    
    // 调试工具
    enableConsole: import.meta.env.VITE_APP_CONSOLE === 'true',
    enableDebug: import.meta.env.VITE_APP_DEBUG === 'true',
  }
}

/**
 * 是否为开发环境
 */
export function isDev() {
  return import.meta.env.DEV
}

/**
 * 是否为生产环境
 */
export function isProd() {
  return import.meta.env.PROD
}

/**
 * 是否为测试环境
 */
export function isTest() {
  return import.meta.env.VITE_APP_ENV === 'test'
}

/**
 * 是否启用调试
 */
export function isDebug() {
  return import.meta.env.VITE_APP_DEBUG === 'true'
}