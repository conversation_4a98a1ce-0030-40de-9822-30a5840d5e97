/*
 * @Date: 2025-06-12 14:12:45
 * @LastEditors: 谢晓静
 * @LastEditTime: 2025-06-12 17:22:42
 * @FilePath: /datax-fe/src/router/index.js
 */
import { createRouter, createWebHistory } from "vue-router";

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: "/createTable",
      name: "createTable",
      component: () => import("../views/CreateTable/index.vue"),
    },
  ],
});

export default router;
