{"name": "datax-fe", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test:unit": "vitest", "lint": "eslint . --fix", "format": "prettier --write .", "commit:comment": "引导设置规范化的提交信息", "commit": "git-cz", "prepare": "husky"}, "dependencies": {"@vtj/icons": "^0.12.40", "@vtj/renderer": "^0.12.40", "@vtj/web": "^0.12.40", "@vueuse/core": "^13.2.0", "axios": "^1.9.0", "echarts": "^5.6.0", "element-plus": "^2.10.2", "lodash": "^4.17.21", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.22.0", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vitest/eslint-plugin": "^1.1.39", "@vtj/cli": "^0.12.4", "@vtj/pro": "^0.12.40", "@vue/eslint-config-prettier": "^10.2.0", "@vue/test-utils": "^2.4.6", "commitizen": "^4.3.1", "commitlint-config-cz": "^0.13.3", "cz-conventional-changelog": "^3.3.0", "cz-customizable": "^7.4.0", "esbuild": "0.25.5", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-vue": "~10.0.0", "globals": "^16.0.0", "husky": "^9.1.7", "jsdom": "^26.0.0", "lint-staged": "^16.0.0", "postcss-px-to-viewport-8-plugin": "^1.2.5", "prettier": "3.5.3", "sass": "^1.89.0", "unplugin-auto-import": "^19.3.0", "unplugin-element-plus": "^0.10.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.2.4", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.1.1"}, "lint-staged": {"*.{js,jsx,ts,tsx,vue}": ["eslint --fix", "prettier --write"]}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}, "cz-customizable": {"config": "./.cz-config.js"}}}