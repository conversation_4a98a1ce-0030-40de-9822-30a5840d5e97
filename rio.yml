schemaVersion: 2.0
timeout: 20
# For reasoning behind freestyle template, please see the following:
# - https://docs.pie.apple.com/artifactory/whats-new.html#permission-changes-in-docker-registry
# - https://stackoverflow.apple.com/questions/13373/how-do-i-fix-the-dockerfilev1publish-extra-tags-build-error

pipelines:
  - machine:
      baseImage: docker.apple.com/pie/fuji:latest
    branchName: dev
    build:
      template: freestyle:v4:publish
      steps:
        - "true"
    package:
      dockerfile:
        - dockerfilePath: Dockerfile
          version: next
          env:
            BASE_VERSION: "dev_0.1.0.${RIO_BUILD_NUMBER}"
            BASE_ENV: "dev"
          extraTags: ["0.1.0.${RIO_BUILD_NUMBER}"]   # Sets another tag for versioning the latest build
          perApplication: false
          publish:
            - repo: docker.apple.com/gc-dmp-dev/datax-fe


  - machine:
      baseImage: docker.apple.com/pie/fuji:latest
    branchName: main
    build:
      template: freestyle:v4:publish
      steps:
        - "true"
    package:
      dockerfile:
        - dockerfilePath: Dockerfile
          version: next
          env:
            BASE_VERSION: "prod_0.1.0.${RIO_BUILD_NUMBER}"
            BASE_ENV: "prod"
          extraTags: ["0.1.0.${RIO_BUILD_NUMBER}"]   # Sets another tag for versioning the latest build
          perApplication: false
          publish:
            - repo: docker.apple.com/gc-dmp-prod/datax-fe
    finally:
      tag:
        expression: "1.1.0.${RIO_BUILD_NUMBER}"
