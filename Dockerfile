FROM node:18 AS node

WORKDIR /tmp/datax-fe/

COPY ./ /tmp/datax-fe/

ARG BASE_VERSION
ARG BASE_ENV
ENV VUE_APP_BASE_VERSION $BASE_VERSION
ENV VUE_APP_BASE_ENV $BASE_ENV

RUN npm install --registry https://npm.apple.com/ && npm run build

FROM nginx 

COPY --from=node /tmp/datax-fe/dist/ /var/www/vue/

RUN ["rm", "-rf", "/etc/nginx/nginx.conf"]
RUN ["rm", "-rf", "/etc/nginx/conf.d/*"]
COPY ./nginx/nginx.conf /etc/nginx/nginx.conf
COPY ./nginx/resolvers.conf /etc/nginx/conf.d/resolvers.conf
RUN ["chmod", "777", "/etc/nginx/nginx.conf"]
RUN ["chmod", "777", "/etc/nginx/conf.d/resolvers.conf"]

EXPOSE 82

RUN ln -sf /dev/stdout /var/log/nginx/access.log \
  && ln -sf /dev/stderr /var/log/nginx/error.log

ENTRYPOINT ["nginx", "-c", "/etc/nginx/nginx.conf", "-g", "daemon off;"]
