# For more information on configuration, see:
#   * Official English Documentation: http://nginx.org/en/docs/
#   * Official Russian Documentation: http://nginx.org/ru/docs/

#user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log info;
pid /run/nginx.pid;

# Load dynamic modules. See /usr/share/doc/nginx/README.dynamic.
include /usr/share/nginx/modules/*.conf;

events {
    worker_connections 1024;
}

http {
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
        '$status $body_bytes_sent "$http_referer" '
        '"$http_user_agent" "$http_x_forwarded_for" '
        '"$upstream_addr" "$upstream_response_time" "$request_time" "$upstream_connect_time"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 300;
    types_hash_max_size 4096;
    client_body_buffer_size 100M;
    client_max_body_size 100M;
    large_client_header_buffers 8 16M;
    fastcgi_intercept_errors on;

    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    proxy_connect_timeout    600;
    proxy_read_timeout       600;
    proxy_send_timeout       600;
    proxy_set_header Host $host;
    proxy_set_header X-real-ip $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;


    # Load modular configuration files from the /etc/nginx/conf.d directory.
    # See http://nginx.org/en/docs/ngx_core_module.html#include
    # for more information.
    include /etc/nginx/conf.d/resolvers.conf;

    server {
        listen 82;
        listen [::]:82;
        server_name _;
        root /usr/share/nginx/html;

        # Load configuration files for the default server block.
        #include /etc/nginx/default.d/*.conf;

        error_page 404 /404.html;
        location = /40x.html {
        }

        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
        }

        location /expert/static/display/ {
            alias /mnt/expert/videos/;
            add_header Cache-Control max-age=31536000;
        }

        location /static/display/ {
            alias /mnt/expert/static/;
            add_header Cache-Control max-age=31536000;
        }

        location ~ ^/static/js/.*\.js\.map$ {
            root /var/www/vue/;
            try_files $uri /empty_response;
        }
    
        location = /empty_response {
            return 200;
        }

        # 处理 JS 文件
        location /static/js/ {
            root /var/www/vue/;
            expires 1y;
            add_header Cache-Control "public, immutable";
            gzip on;
            gzip_types application/javascript;
        }

        # 处理 CSS 文件
        location /static/css/ {
            root /var/www/vue/;
            expires 1y;
            add_header Cache-Control "public, immutable";
            gzip on;
            gzip_types text/css;
        }

        # 处理图片文件
        location /static/images/ {
            root /var/www/vue/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # 处理字体文件
        location /static/fonts/ {
            root /var/www/vue/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # 处理媒体文件
        location /static/media/ {
            root /var/www/vue/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # 处理其他静态资源
        location /static/ {
            root /var/www/vue/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # 处理 public 目录的资源
        location ~* \.(ico|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot)$ {
            root /var/www/vue/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # SPA 路由处理
        location / {
            root /var/www/vue/;
            try_files $uri $uri/ /index.html;
            index index.html index.htm;

            # HTML 文件不缓存
            location ~* \.html$ {
                expires -1;
                add_header Cache-Control "no-cache, no-store, must-revalidate";
            }
        }
    }

}

